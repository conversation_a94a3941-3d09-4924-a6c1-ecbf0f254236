"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _MapDomainModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MapDomainModal */ \"(app-pages-browser)/./src/app/dashboard/MapDomainModal.tsx\");\n/* harmony import */ var _AskDomainModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AskDomainModal */ \"(app-pages-browser)/./src/app/dashboard/AskDomainModal.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst DashboardPage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const [sites, setSites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isMapDomainOpen, setIsMapDomainOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAskDomainOpen, setIsAskDomainOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSiteName, setSelectedSiteName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedSiteId, setSelectedSiteId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Trigger AskDomainModal if redirected from checkout\n        const handlePostCheckout = async ()=>{\n            const postCheckout = searchParams.get(\"postCheckout\");\n            if (postCheckout) {\n                // Check for secure session first (new approach)\n                const sessionParam = searchParams.get(\"session\");\n                if (sessionParam) {\n                    try {\n                        const response = await fetch(\"/api/payment-session?sessionId=\".concat(sessionParam));\n                        if (response.ok) {\n                            const { session } = await response.json();\n                            setSelectedSiteId(session.siteId);\n                            setIsAskDomainOpen(true);\n                            // Clean up the session after use\n                            await fetch(\"/api/payment-session?sessionId=\".concat(sessionParam), {\n                                method: \"DELETE\"\n                            });\n                            // Clean URL parameters\n                            const url = new URL(window.location.href);\n                            url.searchParams.delete(\"postCheckout\");\n                            url.searchParams.delete(\"session\");\n                            window.history.replaceState({}, \"\", url.toString());\n                            return;\n                        }\n                    } catch (error) {\n                        console.error(\"Failed to validate payment session:\", error);\n                    }\n                }\n                // Fallback to old query parameter approach for backward compatibility\n                const siteIdFromParam = searchParams.get(\"siteId\");\n                if (siteIdFromParam) {\n                    setSelectedSiteId(siteIdFromParam);\n                    setIsAskDomainOpen(true);\n                    // Clean URL parameters\n                    const url = new URL(window.location.href);\n                    url.searchParams.delete(\"postCheckout\");\n                    url.searchParams.delete(\"siteId\");\n                    window.history.replaceState({}, \"\", url.toString());\n                }\n            }\n        };\n        handlePostCheckout();\n    }, [\n        searchParams\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSites = async ()=>{\n            try {\n                const { data, error } = await supabase.from(\"user-dashboard\").select(\"id, site_name, expiry_status\");\n                if (error) {\n                    throw error;\n                }\n                setSites(data);\n            } catch (err) {\n                console.error(\"Error fetching sites:\", err);\n                setError(err instanceof Error ? err.message : \"An unknown error occurred\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSites();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 text-center\",\n            children: \"Loading sites...\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n            lineNumber: 104,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 text-center text-red-500\",\n            children: [\n                \"Error: \",\n                error\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n            lineNumber: 108,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full p-8 bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-6 mb-6 bg-white rounded-lg shadow-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-semibold text-gray-800\",\n                        children: \"Websites\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search\",\n                                    className: \"py-2 pl-10 pr-4 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                    value: search,\n                                    onChange: (e)=>setSearch(e.target.value)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"absolute w-5 h-5 text-gray-400 transform -translate-y-1/2 left-3 top-1/2\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden bg-white rounded-lg shadow-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase\",\n                                        children: \"Site Name\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        scope: \"col\",\n                                        className: \"px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase\",\n                                        children: \"Expiry\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: (search.trim() ? sites.filter((site)=>site.site_name.toLowerCase().includes(search.toLowerCase())) : sites).map((site)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"flex items-center px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap\",\n                                            children: [\n                                                site.site_name,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"ml-2 text-gray-400 hover:text-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 text-sm font-medium text-right whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-blue-500 hover:text-blue-700\",\n                                                        title: \"Map Domain\",\n                                                        onClick: ()=>{\n                                                            setSelectedSiteName(site.site_name);\n                                                            setIsMapDomainOpen(true);\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    site.expiry_status === \"Temporary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-green-500 hover:text-green-700\",\n                                                        title: \"Choose Plan\",\n                                                        onClick: ()=>{\n                                                            window.location.href = \"/payments?siteId=\".concat(site.id);\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    site.expiry_status === \"Permanent\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-purple-500 hover:text-purple-700\",\n                                                        title: \"Change Plan\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                    x: \"2\",\n                                                                    y: \"7\",\n                                                                    width: \"20\",\n                                                                    height: \"10\",\n                                                                    rx: \"2\",\n                                                                    ry: \"2\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    fill: \"none\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M2 11h20\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"7\",\n                                                                    cy: \"15\",\n                                                                    r: \"1\",\n                                                                    fill: \"currentColor\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 212,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"11\",\n                                                                    cy: \"15\",\n                                                                    r: \"1\",\n                                                                    fill: \"currentColor\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 text-sm whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 inline-flex text-xs leading-5 font-semibold rounded-full \".concat(site.expiry_status === \"Permanent\" ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"),\n                                                children: site.expiry_status\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, site.id, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 text-sm text-gray-600 bg-white border-t border-gray-200 rounded-b-lg\",\n                children: [\n                    sites.length,\n                    \" Sites\"\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MapDomainModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: isMapDomainOpen,\n                onClose: ()=>setIsMapDomainOpen(false),\n                siteName: selectedSiteName\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AskDomainModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: isAskDomainOpen,\n                onYes: ()=>{\n                    // open map domain modal\n                    const site = sites.find((s)=>s.id === selectedSiteId);\n                    if (site) {\n                        setSelectedSiteName(site.site_name);\n                        setIsMapDomainOpen(true);\n                    }\n                    setIsAskDomainOpen(false);\n                },\n                onNo: ()=>{\n                    router.push(\"/dashboard/domain\");\n                    setIsAskDomainOpen(false);\n                }\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DashboardPage, \"IzuKsNvSsAWFN7mUdVFgBeQmo90=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c = DashboardPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DashboardPage);\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});