/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/create-checkout-session/route";
exports.ids = ["app/api/create-checkout-session/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcreate-checkout-session%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcreate-checkout-session%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_dell_Desktop_wp_ai_app_src_app_api_create_checkout_session_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/create-checkout-session/route.ts */ \"(rsc)/./src/app/api/create-checkout-session/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/create-checkout-session/route\",\n        pathname: \"/api/create-checkout-session\",\n        filename: \"route\",\n        bundlePath: \"app/api/create-checkout-session/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/wp-ai-app/src/app/api/create-checkout-session/route.ts\",\n    nextConfigOutput,\n    userland: _home_dell_Desktop_wp_ai_app_src_app_api_create_checkout_session_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/create-checkout-session/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcreate-checkout-session%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/create-checkout-session/route.ts":
/*!******************************************************!*\
  !*** ./src/app/api/create-checkout-session/route.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n/* harmony import */ var _lib_stripe_plans__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/stripe-plans */ \"(rsc)/./src/lib/stripe-plans.ts\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_secure_session__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/secure-session */ \"(rsc)/./src/lib/secure-session.ts\");\n// /api/create-checkout-session\n\n\n\n\n\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_0__[\"default\"](process.env.STRIPE_SECRET_KEY);\nasync function POST(request) {\n    try {\n        const { planId, isYearly, siteId } = await request.json();\n        // Authenticate user\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createRouteHandlerClient)({\n            cookies: next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies\n        });\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return Response.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const baseUrl = \"http://localhost:3000\" || 0;\n        let paymentSessionId = null;\n        // If siteId is provided, validate ownership and create payment session\n        if (siteId) {\n            const ownership = await (0,_lib_secure_session__WEBPACK_IMPORTED_MODULE_4__.validateSiteOwnership)(siteId, user.id);\n            if (!ownership.valid) {\n                return Response.json({\n                    error: ownership.error || \"Unauthorized access to site\"\n                }, {\n                    status: 403\n                });\n            }\n            // Create secure payment session\n            const sessionManager = new _lib_secure_session__WEBPACK_IMPORTED_MODULE_4__.ServerSessionManager();\n            const sessionResult = await sessionManager.createPaymentSession(siteId, user.id, \"payment\", {\n                planId,\n                isYearly\n            });\n            if (!sessionResult.success) {\n                return Response.json({\n                    error: \"Failed to create payment session\"\n                }, {\n                    status: 500\n                });\n            }\n            paymentSessionId = sessionResult.sessionId;\n        }\n        // Get user's Stripe customer ID (you'll need to implement this)\n        const stripeCustomerId = user.user_metadata?.stripe_customer_id || process.env.STRIPE_TEST_CUSTOMER_ID;\n        const session = await stripe.checkout.sessions.create({\n            mode: \"subscription\",\n            payment_method_types: [\n                \"card\"\n            ],\n            customer: stripeCustomerId,\n            line_items: [\n                {\n                    price: (0,_lib_stripe_plans__WEBPACK_IMPORTED_MODULE_1__.getPriceId)(planId, isYearly),\n                    quantity: 1\n                }\n            ],\n            metadata: {\n                planId,\n                isYearly: String(isYearly),\n                siteId: siteId || \"\",\n                paymentSessionId: paymentSessionId || \"\",\n                userId: user.id\n            },\n            success_url: `${baseUrl}/dashboard?postCheckout=1${paymentSessionId ? `&session=${paymentSessionId}` : \"\"}`,\n            cancel_url: `${baseUrl}/payments${paymentSessionId ? `?session=${paymentSessionId}` : \"\"}`\n        });\n        return Response.json({\n            url: session.url\n        });\n    } catch (error) {\n        console.error(\"Error creating checkout session:\", error);\n        return new Response(JSON.stringify({\n            error: \"Failed to create checkout session\"\n        }), {\n            status: 500,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/create-checkout-session/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/secure-session.ts":
/*!***********************************!*\
  !*** ./src/lib/secure-session.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecureSessionManager: () => (/* binding */ SecureSessionManager),\n/* harmony export */   ServerSessionManager: () => (/* binding */ ServerSessionManager),\n/* harmony export */   validateSiteOwnership: () => (/* binding */ validateSiteOwnership)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n\n\n/**\n * Client-side secure session management\n */ class SecureSessionManager {\n    static{\n        this.SESSION_KEY = \"wp_ai_payment_session\";\n    }\n    static{\n        this.SESSION_EXPIRY_HOURS = 24;\n    }\n    /**\n   * Store session data securely in sessionStorage\n   */ static storeSession(sessionId) {\n        if (true) return;\n        const sessionData = {\n            sessionId,\n            timestamp: Date.now(),\n            expiresAt: Date.now() + this.SESSION_EXPIRY_HOURS * 60 * 60 * 1000\n        };\n        sessionStorage.setItem(this.SESSION_KEY, JSON.stringify(sessionData));\n    }\n    /**\n   * Retrieve session ID from sessionStorage\n   */ static getSessionId() {\n        if (true) return null;\n        try {\n            const stored = sessionStorage.getItem(this.SESSION_KEY);\n            if (!stored) return null;\n            const sessionData = JSON.parse(stored);\n            // Check if session has expired\n            if (Date.now() > sessionData.expiresAt) {\n                this.clearSession();\n                return null;\n            }\n            return sessionData.sessionId;\n        } catch  {\n            this.clearSession();\n            return null;\n        }\n    }\n    /**\n   * Clear session data\n   */ static clearSession() {\n        if (true) return;\n        sessionStorage.removeItem(this.SESSION_KEY);\n    }\n    /**\n   * Check if there's a valid session\n   */ static hasValidSession() {\n        return this.getSessionId() !== null;\n    }\n}\n/**\n * Server-side session management utilities\n */ class ServerSessionManager {\n    constructor(){\n        this.supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n    }\n    /**\n   * Create a new secure payment session\n   */ async createPaymentSession(siteId, userId, purpose = \"payment\", metadata) {\n        try {\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const expiresAt = new Date();\n            expiresAt.setHours(expiresAt.getHours() + 24); // 24 hour expiry\n            const session = {\n                siteId,\n                userId,\n                purpose,\n                expiresAt,\n                createdAt: new Date(),\n                metadata\n            };\n            // Store in database (we'll create this table)\n            const { error } = await this.supabase.from(\"payment_sessions\").insert({\n                id: sessionId,\n                site_id: siteId,\n                user_id: userId,\n                purpose,\n                expires_at: expiresAt.toISOString(),\n                created_at: new Date().toISOString(),\n                metadata: metadata || {}\n            });\n            if (error) {\n                console.error(\"Failed to create payment session:\", error);\n                return {\n                    success: false,\n                    error: \"Failed to create session\"\n                };\n            }\n            return {\n                success: true,\n                sessionId\n            };\n        } catch (error) {\n            console.error(\"Error creating payment session:\", error);\n            return {\n                success: false,\n                error: \"Internal server error\"\n            };\n        }\n    }\n    /**\n   * Validate and retrieve a payment session\n   */ async validateSession(sessionId) {\n        try {\n            if (!sessionId) {\n                return {\n                    valid: false,\n                    error: \"Session ID required\"\n                };\n            }\n            const { data, error } = await this.supabase.from(\"payment_sessions\").select(\"*\").eq(\"id\", sessionId).single();\n            if (error || !data) {\n                return {\n                    valid: false,\n                    error: \"Session not found\"\n                };\n            }\n            // Check if session has expired\n            const expiresAt = new Date(data.expires_at);\n            if (new Date() > expiresAt) {\n                // Clean up expired session\n                await this.deleteSession(sessionId);\n                return {\n                    valid: false,\n                    error: \"Session expired\"\n                };\n            }\n            const session = {\n                id: data.id,\n                siteId: data.site_id,\n                userId: data.user_id,\n                purpose: data.purpose,\n                expiresAt: new Date(data.expires_at),\n                createdAt: new Date(data.created_at),\n                metadata: data.metadata || {}\n            };\n            return {\n                valid: true,\n                session\n            };\n        } catch (error) {\n            console.error(\"Error validating session:\", error);\n            return {\n                valid: false,\n                error: \"Internal server error\"\n            };\n        }\n    }\n    /**\n   * Delete a payment session\n   */ async deleteSession(sessionId) {\n        try {\n            const { error } = await this.supabase.from(\"payment_sessions\").delete().eq(\"id\", sessionId);\n            return !error;\n        } catch (error) {\n            console.error(\"Error deleting session:\", error);\n            return false;\n        }\n    }\n    /**\n   * Clean up expired sessions\n   */ async cleanupExpiredSessions() {\n        try {\n            await this.supabase.from(\"payment_sessions\").delete().lt(\"expires_at\", new Date().toISOString());\n        } catch (error) {\n            console.error(\"Error cleaning up expired sessions:\", error);\n        }\n    }\n}\n/**\n * Validate site ownership\n */ async function validateSiteOwnership(siteId, userId) {\n    try {\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n        const { data, error } = await supabase.from(\"user-dashboard\").select(\"id, user_id\").eq(\"id\", siteId).single();\n        if (error) {\n            return {\n                valid: false,\n                error: \"Site not found\"\n            };\n        }\n        // Check if user owns the site\n        if (data.user_id !== userId) {\n            return {\n                valid: false,\n                error: \"Unauthorized access to site\"\n            };\n        }\n        return {\n            valid: true\n        };\n    } catch (error) {\n        console.error(\"Error validating site ownership:\", error);\n        return {\n            valid: false,\n            error: \"Internal server error\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/secure-session.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-plans.ts":
/*!*********************************!*\
  !*** ./src/lib/stripe-plans.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getPriceId: () => (/* binding */ getPriceId),\n/* harmony export */   plans: () => (/* binding */ plans)\n/* harmony export */ });\nconst plans = {\n    starter: {\n        monthly: \"price_1Rh6gZHKmibvltr1B4VM6bW3\",\n        yearly: \"price_1Rl1IwHKmibvltr1UqjhoNk8\"\n    },\n    pro: {\n        monthly: \"price_1PLaO5HKmibvltr1AFSWYvYl\",\n        yearly: \"price_1PLaO5HKmibvltr1Q22gVz8P\"\n    },\n    turbo: {\n        monthly: \"price_1PLaOKHKmibvltr18ySjBqDB\",\n        yearly: \"price_1PLaOKHKmibvltr1yC4hTRs8\"\n    }\n};\nconst getPriceId = (planId, isYearly)=>{\n    const plan = plans[planId];\n    if (!plan) {\n        throw new Error(`Plan not found: ${planId}`);\n    }\n    return isYearly ? plan.yearly : plan.monthly;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N0cmlwZS1wbGFucy50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPLE1BQU1BLFFBQVE7SUFDbkJDLFNBQVM7UUFDUEMsU0FBUztRQUNUQyxRQUFRO0lBQ1Y7SUFDQUMsS0FBSztRQUNIRixTQUFTO1FBQ1RDLFFBQVE7SUFDVjtJQUNBRSxPQUFPO1FBQ0xILFNBQVM7UUFDVEMsUUFBUTtJQUNWO0FBQ0YsRUFBRTtBQUVLLE1BQU1HLGFBQWEsQ0FBQ0MsUUFBZ0JDO0lBQ3pDLE1BQU1DLE9BQU9ULEtBQUssQ0FBQ08sT0FBTztJQUMxQixJQUFJLENBQUNFLE1BQU07UUFDVCxNQUFNLElBQUlDLE1BQU0sQ0FBQyxnQkFBZ0IsRUFBRUgsT0FBTyxDQUFDO0lBQzdDO0lBQ0EsT0FBT0MsV0FBV0MsS0FBS04sTUFBTSxHQUFHTSxLQUFLUCxPQUFPO0FBQzlDLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vc3JjL2xpYi9zdHJpcGUtcGxhbnMudHM/ODUwNyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgcGxhbnMgPSB7XG4gIHN0YXJ0ZXI6IHtcbiAgICBtb250aGx5OiAncHJpY2VfMVJoNmdaSEttaWJ2bHRyMUI0Vk02YlczJyxcbiAgICB5ZWFybHk6ICdwcmljZV8xUmwxSXdIS21pYnZsdHIxVXFqaG9OazgnLFxuICB9LFxuICBwcm86IHtcbiAgICBtb250aGx5OiAncHJpY2VfMVBMYU81SEttaWJ2bHRyMUFGU1dZdllsJyxcbiAgICB5ZWFybHk6ICdwcmljZV8xUExhTzVIS21pYnZsdHIxUTIyZ1Z6OFAnLFxuICB9LFxuICB0dXJibzoge1xuICAgIG1vbnRobHk6ICdwcmljZV8xUExhT0tIS21pYnZsdHIxOHlTakJxREInLFxuICAgIHllYXJseTogJ3ByaWNlXzFQTGFPS0hLbWlidmx0cjF5QzRoVFJzOCcsXG4gIH0sXG59O1xuXG5leHBvcnQgY29uc3QgZ2V0UHJpY2VJZCA9IChwbGFuSWQ6IHN0cmluZywgaXNZZWFybHk6IGJvb2xlYW4pOiBzdHJpbmcgPT4ge1xuICBjb25zdCBwbGFuID0gcGxhbnNbcGxhbklkXTtcbiAgaWYgKCFwbGFuKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKGBQbGFuIG5vdCBmb3VuZDogJHtwbGFuSWR9YCk7XG4gIH1cbiAgcmV0dXJuIGlzWWVhcmx5ID8gcGxhbi55ZWFybHkgOiBwbGFuLm1vbnRobHk7XG59O1xuIl0sIm5hbWVzIjpbInBsYW5zIiwic3RhcnRlciIsIm1vbnRobHkiLCJ5ZWFybHkiLCJwcm8iLCJ0dXJibyIsImdldFByaWNlSWQiLCJwbGFuSWQiLCJpc1llYXJseSIsInBsYW4iLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-plans.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/isows","vendor-chunks/stripe","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/uuid","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/hasown","vendor-chunks/get-intrinsic","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcreate-checkout-session%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();