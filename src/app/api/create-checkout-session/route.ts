// /api/create-checkout-session
import <PERSON><PERSON> from 'stripe'
import { getPriceId } from '@/lib/stripe-plans';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { ServerSessionManager, validateSiteOwnership } from '@/lib/secure-session';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY)

export async function POST(request: Request) {
  try {
    const { planId, isYearly, siteId } = await request.json()

    // Authenticate user
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    let paymentSessionId = null;

    // If siteId is provided, validate ownership and create payment session
    if (siteId) {
      const ownership = await validateSiteOwnership(siteId, user.id);
      if (!ownership.valid) {
        return Response.json({ error: ownership.error || 'Unauthorized access to site' }, { status: 403 });
      }

      // Create secure payment session
      const sessionManager = new ServerSessionManager();
      const sessionResult = await sessionManager.createPaymentSession(
        siteId,
        user.id,
        'payment',
        { planId, isYearly }
      );

      if (!sessionResult.success) {
        return Response.json({ error: 'Failed to create payment session' }, { status: 500 });
      }

      paymentSessionId = sessionResult.sessionId;
    }

    // Get user's Stripe customer ID (you'll need to implement this)
    const stripeCustomerId = user.user_metadata?.stripe_customer_id || process.env.STRIPE_TEST_CUSTOMER_ID;

    const session = await stripe.checkout.sessions.create({
      mode: 'subscription',
      payment_method_types: ['card'],
      customer: stripeCustomerId,
      line_items: [
        {
          price: getPriceId(planId, isYearly),
          quantity: 1,
        },
      ],
      metadata: {
        planId,
        isYearly: String(isYearly),
        siteId: siteId || '',
        paymentSessionId: paymentSessionId || '',
        userId: user.id,
      },
      success_url: `${baseUrl}/dashboard?postCheckout=1${paymentSessionId ? `&session=${paymentSessionId}` : ''}`,
      cancel_url: `${baseUrl}/payments${paymentSessionId ? `?session=${paymentSessionId}` : ''}`,

    })

    return Response.json({ url: session.url })
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return new Response(JSON.stringify({ error: 'Failed to create checkout session' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}