import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { ServerSessionManager, validateSiteOwnership } from '@/lib/secure-session';

/**
 * Create a new payment session
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { siteId, purpose = 'payment', metadata } = await request.json();

    if (!siteId) {
      return NextResponse.json({ error: 'Site ID is required' }, { status: 400 });
    }

    // Validate site ownership
    const ownership = await validateSiteOwnership(siteId, user.id);
    if (!ownership.valid) {
      return NextResponse.json({ error: ownership.error || 'Unauthorized access' }, { status: 403 });
    }

    // Create payment session
    const sessionManager = new ServerSessionManager();
    const result = await sessionManager.createPaymentSession(siteId, user.id, purpose, metadata);

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true, 
      sessionId: result.sessionId 
    });

  } catch (error) {
    console.error('Error creating payment session:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Validate and retrieve a payment session
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    const sessionManager = new ServerSessionManager();
    const result = await sessionManager.validateSession(sessionId);

    if (!result.valid) {
      return NextResponse.json({ error: result.error }, { status: 404 });
    }

    // Verify the user making the request owns this session
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user || user.id !== result.session?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    return NextResponse.json({ 
      success: true, 
      session: result.session 
    });

  } catch (error) {
    console.error('Error validating payment session:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Delete a payment session
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    // Verify user owns this session
    const sessionManager = new ServerSessionManager();
    const validation = await sessionManager.validateSession(sessionId);

    if (!validation.valid) {
      return NextResponse.json({ error: 'Session not found' }, { status: 404 });
    }

    const supabase = createRouteHandlerClient({ cookies });
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user || user.id !== validation.session?.userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Delete the session
    const deleted = await sessionManager.deleteSession(sessionId);

    if (!deleted) {
      return NextResponse.json({ error: 'Failed to delete session' }, { status: 500 });
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error deleting payment session:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
