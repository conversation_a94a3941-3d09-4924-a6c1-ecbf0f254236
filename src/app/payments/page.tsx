"use client"
import { useState, useEffect } from "react"
import { SecureSessionManager } from '@/lib/secure-session'

const PricingPage = () => {
  const [isYearly, setIsYearly] = useState(false)
  // Change: Track loading per plan
  const [loadingPlanId, setLoadingPlanId] = useState<string | null>(null)
  const [siteId, setSiteId] = useState<string | null>(null)
  const [sessionId, setSessionId] = useState<string | null>(null)

  const plans = [
    {
      id: "starter",
      name: "Starter",
      price: 5,
      yearlyPrice: 3,
      description: "Perfect for individuals and small projects",
      features: [
        "10 GB CDN bandwidth",
        "10 GB disk storage",
        "Weekly backup schedule",
        "Domain mapping",
        "Standard support",
        "SSL certificates",
        "Basic analytics",
      ],
      popular: false,
      buttonText: "Get Starter",
      buttonStyle: "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white",
    },
    {
      id: "pro",
      name: "Pro",
      price: 29,
      yearlyPrice: 24,
      description: "Ideal for growing businesses and agencies",
      features: [
        "75 GB CDN bandwidth",
        "35 GB disk storage",
        "Daily backup schedule",
        "Domain mapping",
        "Priority support",
        "100GB storage",
        "SSL certificates",
        "Advanced analytics",
        "Ecommerce availability",
      ],
      popular: true,
      buttonText: "Get Pro",
      buttonStyle: "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white",
    },
    {
      id: "turbo",
      name: "Turbo",
      price: 59,
      yearlyPrice: 49,
      description: "For high-traffic sites and advanced teams",
      features: [
        "125 GB CDN bandwidth",
        "50 GB disk storage",
        "Daily backup schedule",
        "Domain mapping",
        "Dedicated support",
        "SSL certificates",
        "Advanced analytics",
        "Custom integrations",
        "SLA guarantee",
        "Ecommerce availability",
      ],
      popular: false,
      buttonText: "Get Turbo",
      buttonStyle: "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white",
    },
    {
      id: "enterprise",
      name: "Enterprise",
      price: "Custom",
      yearlyPrice: "Custom",
      description: "For large organizations with advanced needs",
      features: [
        "200 GB CDN bandwidth",
        "75 GB disk storage",
        "Daily backup schedule",
        "Everything in Turbo",
        "Custom integrations",
        "Advanced security",
        "Dedicated support",
        "SLA guarantees",
        "Ecommerce availability",
      ],
      popular: false,
      buttonText: "Contact Sales",
      buttonStyle: "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white",
    },
  ]

  // Load siteId from secure session or URL parameters
  useEffect(() => {
    const loadSiteContext = async () => {
      // First check for session parameter (new secure approach)
      const urlParams = new URLSearchParams(window.location.search);
      const sessionParam = urlParams.get('session');

      if (sessionParam) {
        setSessionId(sessionParam);

        // Validate session and get siteId
        try {
          const response = await fetch(`/api/payment-session?sessionId=${sessionParam}`);
          if (response.ok) {
            const { session } = await response.json();
            setSiteId(session.siteId);
            return;
          }
        } catch (error) {
          console.error('Failed to validate payment session:', error);
        }
      }

      // Fallback to old query parameter approach for backward compatibility
      const siteIdParam = urlParams.get('siteId');
      if (siteIdParam) {
        setSiteId(siteIdParam);
      }
    };

    loadSiteContext();
  }, []);

  const handlePlanSelect = async (planId: string) => {
    if (planId === "enterprise") {
      window.location.href = "mailto:<EMAIL>"
      return
    }

    setLoadingPlanId(planId)

    try {
      const response = await fetch("/api/create-checkout-session", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          planId, 
          isYearly,
          siteId,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to create checkout session")
      }

      const { url } = await response.json()
      window.location.href = url
    } catch (error) {
      console.error("Error creating checkout session:", error)
      alert("Something went wrong. Please try again.")
      setLoadingPlanId(null)
    }
  }

  return (
    <div className="min-h-screen text-slate-800 bg-gradient-to-br from-green-50 to-green-100">
      {/* Header */}
      <div className="px-4 py-8 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div className="mb-12 text-center">
          <h1 className="mb-4 text-4xl font-bold text-slate-800 md:text-6xl">Pricing</h1>
          <p className="mb-8 text-xl text-slate-600">Choose the plan that works for you</p>

          {/* Billing Toggle */}
          <div className="inline-flex items-center p-1 mb-12 bg-white border border-green-200 rounded-lg shadow-sm">
            <button
              onClick={() => setIsYearly(false)}
              className={`px-6 py-2 rounded-md text-sm font-medium transition-all ${
                !isYearly ? "bg-green-600 text-white shadow-sm" : "text-slate-600 hover:text-slate-800"
              }`}
            >
              MONTHLY
            </button>
            <button
              onClick={() => setIsYearly(true)}
              className={`px-6 py-2 rounded-md text-sm font-medium transition-all ${
                isYearly ? "bg-green-600 text-white shadow-sm" : "text-slate-600 hover:text-slate-800"
              }`}
            >
              YEARLY
              <span className="px-2 py-1 ml-2 text-xs text-green-700 bg-green-100 rounded">SAVE 20%</span>
            </button>
          </div>
        </div>

        {/* Pricing Plans Section */}
        <div className="mb-12">
          <h2 className="mb-8 text-2xl font-bold text-center text-slate-800">Plans</h2>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
            {plans.map((plan) => (
              <div
                key={plan.id}
                className={`relative flex flex-col h-full min-h-[520px] rounded-2xl p-6 transition-all duration-300 ${
                  plan.popular
                    ? "bg-white border-2 border-green-400 shadow-xl ring-2 ring-green-200"
                    : "bg-white border border-green-200 shadow-sm"
                } hover:scale-105 hover:shadow-xl hover:z-20 hover:border-green-400`}
                style={{ boxSizing: "border-box" }}
              >
                {plan.popular && (
                  <div className="absolute px-3 py-1 text-xs font-medium text-white rounded-full -top-3 left-6 bg-gradient-to-r from-green-600 to-green-700">
                    Most Popular
                  </div>
                )}
                <div className="mb-6">
                  <h3 className="mb-2 text-xl font-bold text-slate-800">{plan.name}</h3>
                  <div className="mb-4">
                    {plan.price === "Custom" ? (
                      <span className="text-4xl font-bold text-slate-800">Custom</span>
                    ) : (
                      <div className="flex items-baseline">
                        <span className="text-4xl font-bold text-slate-800">
                          ${isYearly ? plan.yearlyPrice : plan.price}
                        </span>
                        <span className="ml-1 text-slate-500">/mo</span>
                        {isYearly && typeof plan.yearlyPrice === 'number' && (
                          <span className="ml-2 text-xs text-slate-500">(Billed yearly: ${plan.yearlyPrice * 12})</span>
                        )}
                      </div>
                    )}
                  </div>
                  <p className="mb-6 text-sm text-slate-600">{plan.description}</p>
                </div>
                <div className="mb-8">
                  <h4 className="mb-4 text-sm font-medium text-slate-700">Includes</h4>
                  <ul className="space-y-3">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <svg
                          className="h-5 w-5 text-green-600 mr-3 mt-0.5 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="text-sm text-slate-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="mt-auto">
                  <button
                    onClick={() => handlePlanSelect(plan.id)}
                    disabled={loadingPlanId === plan.id}
                    className={`w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 ${
                      loadingPlanId === plan.id ? "bg-slate-400 cursor-not-allowed" : plan.buttonStyle
                    }`}
                  >
                    {loadingPlanId === plan.id ? (
                      <div className="flex items-center justify-center">
                        <div className="w-4 h-4 mr-2 border-b-2 border-white rounded-full animate-spin"></div>
                        Redirecting...
                      </div>
                    ) : (
                      plan.buttonText
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* FAQ Section */}
        <div className="max-w-4xl mx-auto">
          <h2 className="mb-8 text-2xl font-bold text-center text-slate-800">Frequently Asked Questions</h2>

          <div className="space-y-6">
            {[
              {
                question: "Can I change my plan at any time?",
                answer:
                  "Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.",
              },
              {
                question: "Is there a free trial available?",
                answer: "Yes, we offer a 14-day free trial for all paid plans. No credit card required to get started.",
              },
              {
                question: "What payment methods do you accept?",
                answer: "We accept all major credit cards, PayPal, and bank transfers for annual plans.",
              },
              {
                question: "Do you offer refunds?",
                answer:
                  "Yes, we offer a 30-day money-back guarantee. If you're not satisfied, we'll refund your payment.",
              },
            ].map((faq, index) => (
              <div key={index} className="pb-6 border-b border-green-200">
                <h3 className="mb-2 text-lg font-semibold text-slate-800">{faq.question}</h3>
                <p className="text-slate-600">{faq.answer}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default PricingPage