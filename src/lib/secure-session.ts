import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { v4 as uuidv4 } from 'uuid';

export interface PaymentSession {
  id: string;
  siteId: string;
  userId: string;
  purpose: 'payment' | 'domain_selection';
  expiresAt: Date;
  createdAt: Date;
  metadata?: Record<string, any>;
}

export interface SecureSessionResponse {
  success: boolean;
  sessionId?: string;
  error?: string;
}

export interface SessionValidationResponse {
  valid: boolean;
  session?: PaymentSession;
  error?: string;
}

/**
 * Client-side secure session management
 */
export class SecureSessionManager {
  private static readonly SESSION_KEY = 'wp_ai_payment_session';
  private static readonly SESSION_EXPIRY_HOURS = 24;

  /**
   * Store session data securely in sessionStorage
   */
  static storeSession(sessionId: string): void {
    if (typeof window === 'undefined') return;
    
    const sessionData = {
      sessionId,
      timestamp: Date.now(),
      expiresAt: Date.now() + (this.SESSION_EXPIRY_HOURS * 60 * 60 * 1000)
    };
    
    sessionStorage.setItem(this.SESSION_KEY, JSON.stringify(sessionData));
  }

  /**
   * Retrieve session ID from sessionStorage
   */
  static getSessionId(): string | null {
    if (typeof window === 'undefined') return null;
    
    try {
      const stored = sessionStorage.getItem(this.SESSION_KEY);
      if (!stored) return null;
      
      const sessionData = JSON.parse(stored);
      
      // Check if session has expired
      if (Date.now() > sessionData.expiresAt) {
        this.clearSession();
        return null;
      }
      
      return sessionData.sessionId;
    } catch {
      this.clearSession();
      return null;
    }
  }

  /**
   * Clear session data
   */
  static clearSession(): void {
    if (typeof window === 'undefined') return;
    sessionStorage.removeItem(this.SESSION_KEY);
  }

  /**
   * Check if there's a valid session
   */
  static hasValidSession(): boolean {
    return this.getSessionId() !== null;
  }
}

/**
 * Server-side session management utilities
 */
export class ServerSessionManager {
  private supabase;

  constructor() {
    this.supabase = createClientComponentClient();
  }

  /**
   * Create a new secure payment session
   */
  async createPaymentSession(
    siteId: string, 
    userId: string, 
    purpose: PaymentSession['purpose'] = 'payment',
    metadata?: Record<string, any>
  ): Promise<SecureSessionResponse> {
    try {
      const sessionId = uuidv4();
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24); // 24 hour expiry

      const session: Omit<PaymentSession, 'id'> = {
        siteId,
        userId,
        purpose,
        expiresAt,
        createdAt: new Date(),
        metadata
      };

      // Store in database (we'll create this table)
      const { error } = await this.supabase
        .from('payment_sessions')
        .insert({
          id: sessionId,
          site_id: siteId,
          user_id: userId,
          purpose,
          expires_at: expiresAt.toISOString(),
          created_at: new Date().toISOString(),
          metadata: metadata || {}
        });

      if (error) {
        console.error('Failed to create payment session:', error);
        return { success: false, error: 'Failed to create session' };
      }

      return { success: true, sessionId };
    } catch (error) {
      console.error('Error creating payment session:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Validate and retrieve a payment session
   */
  async validateSession(sessionId: string): Promise<SessionValidationResponse> {
    try {
      if (!sessionId) {
        return { valid: false, error: 'Session ID required' };
      }

      const { data, error } = await this.supabase
        .from('payment_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (error || !data) {
        return { valid: false, error: 'Session not found' };
      }

      // Check if session has expired
      const expiresAt = new Date(data.expires_at);
      if (new Date() > expiresAt) {
        // Clean up expired session
        await this.deleteSession(sessionId);
        return { valid: false, error: 'Session expired' };
      }

      const session: PaymentSession = {
        id: data.id,
        siteId: data.site_id,
        userId: data.user_id,
        purpose: data.purpose,
        expiresAt: new Date(data.expires_at),
        createdAt: new Date(data.created_at),
        metadata: data.metadata || {}
      };

      return { valid: true, session };
    } catch (error) {
      console.error('Error validating session:', error);
      return { valid: false, error: 'Internal server error' };
    }
  }

  /**
   * Delete a payment session
   */
  async deleteSession(sessionId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('payment_sessions')
        .delete()
        .eq('id', sessionId);

      return !error;
    } catch (error) {
      console.error('Error deleting session:', error);
      return false;
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<void> {
    try {
      await this.supabase
        .from('payment_sessions')
        .delete()
        .lt('expires_at', new Date().toISOString());
    } catch (error) {
      console.error('Error cleaning up expired sessions:', error);
    }
  }
}

/**
 * Validate site ownership
 */
export async function validateSiteOwnership(siteId: string, userId: string): Promise<{ valid: boolean; error?: string }> {
  try {
    const supabase = createClientComponentClient();
    
    const { data, error } = await supabase
      .from('user-dashboard')
      .select('id, user_id')
      .eq('id', siteId)
      .single();

    if (error) {
      return { valid: false, error: 'Site not found' };
    }

    // Check if user owns the site
    if (data.user_id !== userId) {
      return { valid: false, error: 'Unauthorized access to site' };
    }

    return { valid: true };
  } catch (error) {
    console.error('Error validating site ownership:', error);
    return { valid: false, error: 'Internal server error' };
  }
}
